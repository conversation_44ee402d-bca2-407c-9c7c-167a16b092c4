import React, { useState, useRef } from 'react';
import { Formik, Form, Field, ErrorMessage, FormikProps } from 'formik';
import { step5Schema } from '../utils/validations';
import { Step5Data, VisaFormData } from '../utils/types';
import { searchApplicationsByPassport } from '../utils/supabase';

interface Step5Props {
  initialValues: Step5Data;
  onSubmit: (values: Step5Data) => void;
}

interface PreviousApplication {
  id: string;
  agent_id: string;
  form_data: VisaFormData;
  step_status: number;
  uploaded_files: Record<string, string>;
  whatsapp_redirected: boolean;
  created_at: string;
  updated_at: string;
}

const Step5_VisaHistory: React.FC<Step5Props> = ({ initialValues, onSubmit }) => {
  const [searchPassport, setSearchPassport] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState('');
  const [previousApplication, setPreviousApplication] = useState<PreviousApplication | null>(null);
  const formRef = useRef<FormikProps<Step5Data>>(null);

  const handleSearchPreviousApplication = async () => {
    if (!searchPassport) {
      setSearchError('Введите номер паспорта для поиска');
      return;
    }

    setIsSearching(true);
    setSearchError('');
    setPreviousApplication(null);

    try {
      const { data, error } = await searchApplicationsByPassport(searchPassport);
      
      if (error) {
        console.error('API error:', error);
        throw new Error('Ошибка при поиске анкеты');
      }
      
      if (data) {
        console.log('Found application:', data);
        setPreviousApplication(data);
      } else {
        setSearchError('Предыдущая анкета не найдена. Проверьте номер паспорта.');
      }
    } catch (err) {
      setSearchError('Ошибка при поиске анкеты. Пожалуйста, попробуйте еще раз.');
      console.error('Error searching for previous application:', err);
    } finally {
      setIsSearching(false);
    }
  };

  const loadPreviousApplicationData = () => {
    if (!previousApplication || !formRef.current) return;
    
    try {
      const prevFormData = previousApplication.form_data;
      
      // Set values in the current form
      formRef.current.setValues({
        ...formRef.current.values,
        hasBeenToUSA: prevFormData.hasBeenToUSA || formRef.current.values.hasBeenToUSA,
        hasUSVisa: prevFormData.hasUSVisa || formRef.current.values.hasUSVisa,
        lastVisaDate: prevFormData.lastVisaDate || formRef.current.values.lastVisaDate,
        visaNumber: prevFormData.visaNumber || formRef.current.values.visaNumber,
        isSameVisaType: prevFormData.isSameVisaType || formRef.current.values.isSameVisaType,
        isSameCountry: prevFormData.isSameCountry || formRef.current.values.isSameCountry,
        hasVisaRejections: true, // Force this to true since we're loading a rejected application
        rejectionVisaType: prevFormData.rejectionVisaType || formRef.current.values.rejectionVisaType,
        rejectionDate: prevFormData.rejectionDate || formRef.current.values.rejectionDate,
      });
    } catch (err) {
      console.error('Error loading previous application data:', err);
      setSearchError('Ошибка при загрузке данных из предыдущей анкеты.');
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">История виз</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, предоставьте информацию о ваших предыдущих визах в США, если таковые имеются.
      </p>

      <div className="p-4 border border-gray-200 rounded-lg mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Загрузить данные из предыдущей анкеты</h3>
        <p className="text-gray-600 mb-4">
          Если вы ранее подавали на визу и вам отказали, вы можете загрузить данные из предыдущей анкеты.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="col-span-2">
            <input
              type="text"
              value={searchPassport}
              onChange={(e) => setSearchPassport(e.target.value)}
              placeholder="Введите номер паспорта"
              className="form-input w-full"
            />
          </div>
          <div>
            <button
              type="button"
              onClick={handleSearchPreviousApplication}
              disabled={isSearching}
              className="btn-primary w-full"
            >
              {isSearching ? 'Поиск...' : 'Поиск'}
            </button>
          </div>
        </div>
        
        {searchError && (
          <div className="text-red-500 text-sm mb-4">{searchError}</div>
        )}
        
        {previousApplication && (
          <div className="bg-green-50 p-4 rounded-md mb-4">
            <p className="text-green-800 font-medium">Найдена предыдущая анкета!</p>
            <p className="text-green-600 text-sm">
              Дата создания: {new Date(previousApplication.created_at).toLocaleDateString()}
            </p>
            <button
              type="button"
              onClick={loadPreviousApplicationData}
              className="btn-secondary mt-2"
            >
              Загрузить данные
            </button>
          </div>
        )}
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={step5Schema}
        onSubmit={onSubmit}
      >
        {(formikProps) => {
          // Store reference to formik props for use in loadPreviousApplicationData
          formRef.current = formikProps;
          
          return (
            <Form>
              <div className="grid grid-cols-1 gap-4">
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="hasBeenToUSA"
                      name="hasBeenToUSA"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="hasBeenToUSA" className="ml-2 block text-gray-700 font-medium">
                      Были ли вы в США ранее?
                    </label>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="hasUSVisa"
                      name="hasUSVisa"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="hasUSVisa" className="ml-2 block text-gray-700 font-medium">
                      Имели ли вы ранее визу США?
                    </label>
                  </div>

                  {formikProps.values.hasUSVisa && (
                    <div className="mt-4 mb-4 p-4 border border-gray-200 rounded-md">
                      <div className="mb-4">
                        <label htmlFor="lastVisaDate" className="block text-gray-700 font-medium mb-2">
                          Дата последней визы
                        </label>
                        <Field
                          type="date"
                          id="lastVisaDate"
                          name="lastVisaDate"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="lastVisaDate"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="visaNumber" className="block text-gray-700 font-medium mb-2">
                          Номер визы
                        </label>
                        <Field
                          type="text"
                          id="visaNumber"
                          name="visaNumber"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="visaNumber"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <div className="flex items-center mb-2">
                          <Field
                            type="checkbox"
                            id="isSameVisaType"
                            name="isSameVisaType"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          />
                          <label htmlFor="isSameVisaType" className="ml-2 block text-gray-700 font-medium">
                            Подаете ли вы на такой же тип визы?
                          </label>
                        </div>
                      </div>

                      <div className="mb-4">
                        <div className="flex items-center mb-2">
                          <Field
                            type="checkbox"
                            id="isSameCountry"
                            name="isSameCountry"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          />
                          <label htmlFor="isSameCountry" className="ml-2 block text-gray-700 font-medium">
                            Подаете ли вы с той же страны, где получали визу?
                          </label>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="hasVisaRejections"
                      name="hasVisaRejections"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="hasVisaRejections" className="ml-2 block text-gray-700 font-medium">
                      Были ли отказы в визе США?
                    </label>
                  </div>

                  {formikProps.values.hasVisaRejections && (
                    <div className="mt-4 mb-4 p-4 border border-gray-200 rounded-md">
                      <div className="mb-4">
                        <label htmlFor="rejectionVisaType" className="block text-gray-700 font-medium mb-2">
                          Тип визы
                        </label>
                        <Field
                          type="text"
                          id="rejectionVisaType"
                          name="rejectionVisaType"
                          className="form-input"
                          placeholder="B1/B2, F1, и т.д."
                        />
                        <ErrorMessage
                          name="rejectionVisaType"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div className="mb-4">
                        <label htmlFor="rejectionDate" className="block text-gray-700 font-medium mb-2">
                          Дата отказа
                        </label>
                        <Field
                          type="date"
                          id="rejectionDate"
                          name="rejectionDate"
                          className="form-input"
                        />
                        <ErrorMessage
                          name="rejectionDate"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default Step5_VisaHistory; 