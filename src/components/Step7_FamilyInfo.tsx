import React from 'react';
import { Formik, Form, Field, ErrorMessage, FieldArray } from 'formik';
import { step7Schema } from '../utils/validations';
import { Step7Data } from '../utils/types';

interface Step7Props {
  initialValues: Step7Data;
  onSubmit: (values: Step7Data) => void;
}

const Step7_FamilyInfo: React.FC<Step7Props> = ({ initialValues, onSubmit }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Информация о родителях</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, предоставьте информацию о ваших родителях и родственниках в США.
      </p>

      <Formik
        initialValues={initialValues}
        validationSchema={step7Schema}
        onSubmit={onSubmit}
      >
        {({ values, isValid: _isValid }) => (
          <Form>
            <div className="grid grid-cols-1 gap-4">
              {/* Информация об отце */}
              <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Информация об отце</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="mb-4">
                    <label htmlFor="fatherSurname" className="block text-gray-700 font-medium mb-2">
                      Фамилия
                    </label>
                    <Field
                      type="text"
                      id="fatherSurname"
                      name="fatherSurname"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="fatherSurname"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="fatherName" className="block text-gray-700 font-medium mb-2">
                      Имя
                    </label>
                    <Field
                      type="text"
                      id="fatherName"
                      name="fatherName"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="fatherName"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="isFatherDateOfBirthUnknown"
                      name="isFatherDateOfBirthUnknown"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isFatherDateOfBirthUnknown" className="ml-2 block text-gray-700 font-medium">
                      Неизвестна дата рождения
                    </label>
                  </div>
                </div>

                {!values.isFatherDateOfBirthUnknown && (
                  <div className="mb-4">
                    <label htmlFor="fatherDateOfBirth" className="block text-gray-700 font-medium mb-2">
                      Дата рождения
                    </label>
                    <Field
                      type="date"
                      id="fatherDateOfBirth"
                      name="fatherDateOfBirth"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="fatherDateOfBirth"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                )}

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="isFatherInUSA"
                      name="isFatherInUSA"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isFatherInUSA" className="ml-2 block text-gray-700 font-medium">
                      Проживает ли отец в США?
                    </label>
                  </div>
                  
                  {values.isFatherInUSA && (
                    <div className="mt-2">
                      <Field
                        type="text"
                        id="fatherUSAReason"
                        name="fatherUSAReason"
                        placeholder="Причина нахождения в США"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="fatherUSAReason"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Информация о матери */}
              <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Информация о матери</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="mb-4">
                    <label htmlFor="motherSurname" className="block text-gray-700 font-medium mb-2">
                      Фамилия
                    </label>
                    <Field
                      type="text"
                      id="motherSurname"
                      name="motherSurname"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="motherSurname"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="motherName" className="block text-gray-700 font-medium mb-2">
                      Имя
                    </label>
                    <Field
                      type="text"
                      id="motherName"
                      name="motherName"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="motherName"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="isMotherDateOfBirthUnknown"
                      name="isMotherDateOfBirthUnknown"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isMotherDateOfBirthUnknown" className="ml-2 block text-gray-700 font-medium">
                      Неизвестна дата рождения
                    </label>
                  </div>
                </div>

                {!values.isMotherDateOfBirthUnknown && (
                  <div className="mb-4">
                    <label htmlFor="motherDateOfBirth" className="block text-gray-700 font-medium mb-2">
                      Дата рождения
                    </label>
                    <Field
                      type="date"
                      id="motherDateOfBirth"
                      name="motherDateOfBirth"
                      className="form-input"
                    />
                    <ErrorMessage
                      name="motherDateOfBirth"
                      component="div"
                      className="text-red-500 text-sm mt-1"
                    />
                  </div>
                )}

                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="isMotherInUSA"
                      name="isMotherInUSA"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isMotherInUSA" className="ml-2 block text-gray-700 font-medium">
                      Проживает ли мать в США?
                    </label>
                  </div>
                  
                  {values.isMotherInUSA && (
                    <div className="mt-2">
                      <Field
                        type="text"
                        id="motherUSAReason"
                        name="motherUSAReason"
                        placeholder="Причина нахождения в США"
                        className="form-input"
                      />
                      <ErrorMessage
                        name="motherUSAReason"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Информация о родственниках в США */}
              <div className="mb-6 p-4 border border-gray-200 rounded-lg">
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Field
                      type="checkbox"
                      id="hasRelativesInUSA"
                      name="hasRelativesInUSA"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label htmlFor="hasRelativesInUSA" className="ml-2 block text-gray-700 font-medium">
                      Есть ли у вас родные братья/сестры или другие близкие родственники в США?
                    </label>
                  </div>
                  
                  {values.hasRelativesInUSA && (
                    <div className="mt-4">
                      <FieldArray name="relatives">
                        {({ push, remove }) => (
                          <div>
                            {values.relatives && values.relatives.length > 0 ? (
                              values.relatives.map((relative, index) => (
                                <div key={index} className="flex flex-col md:flex-row gap-4 mb-4">
                                  <div className="flex-1">
                                    <Field
                                      type="text"
                                      name={`relatives.${index}.name`}
                                      placeholder="ФИО родственника"
                                      className="form-input"
                                    />
                                    <ErrorMessage
                                      name={`relatives.${index}.name`}
                                      component="div"
                                      className="text-red-500 text-sm mt-1"
                                    />
                                  </div>
                                  <div className="flex-1">
                                    <Field
                                      type="text"
                                      name={`relatives.${index}.relationship`}
                                      placeholder="Кем приходится (брат, сестра и т.д.)"
                                      className="form-input"
                                    />
                                    <ErrorMessage
                                      name={`relatives.${index}.relationship`}
                                      component="div"
                                      className="text-red-500 text-sm mt-1"
                                    />
                                  </div>
                                  <button
                                    type="button"
                                    className="bg-red-500 text-white px-3 py-2 rounded"
                                    onClick={() => remove(index)}
                                  >
                                    Удалить
                                  </button>
                                </div>
                              ))
                            ) : (
                              <div className="text-gray-500 mb-2">Нет добавленных родственников</div>
                            )}
                            <button
                              type="button"
                              className="bg-blue-500 text-white px-4 py-2 rounded"
                              onClick={() => push({ name: '', relationship: '' })}
                            >
                              Добавить родственника
                            </button>
                          </div>
                        )}
                      </FieldArray>
                      {values.relatives && values.relatives.length === 0 && (
                        <ErrorMessage
                          name="relatives"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Step7_FamilyInfo; 