import React from 'react';
import { Formik, Form, Field, ErrorMessage, FieldArray } from 'formik';
import { step4Schema } from '../utils/validations';
import { Step4Data } from '../utils/types';

interface Step4Props {
  initialValues: Step4Data;
  onSubmit: (values: Step4Data) => void;
}

const travelPurposeOptions = [
  { value: '', label: 'Выберите...' },
  { value: 'tourism', label: 'Туризм' },
  { value: 'business', label: 'Бизнес' },
  { value: 'medical', label: 'Лечение' },
  { value: 'study', label: 'Учеба' },
  { value: 'visa', label: 'Визовая поддержка' },
  { value: 'other', label: 'Другое' },
];

const Step4_TravelPurpose: React.FC<Step4Props> = ({ initialValues, onSubmit }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Цель поездки</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, укажите цель вашей поездки в США и информацию о попутчиках, если таковые имеются.
      </p>

      <Formik
        initialValues={initialValues}
        validationSchema={step4Schema}
        onSubmit={onSubmit}
      >
        {({ values, isValid: _isValid }) => (
          <Form>
            <div className="grid grid-cols-1 gap-4">
              <div className="mb-4">
                <label htmlFor="travelPurpose" className="block text-gray-700 font-medium mb-2">
                  Цель поездки (B1/B2)
                </label>
                <Field
                  as="select"
                  id="travelPurpose"
                  name="travelPurpose"
                  className="form-input"
                >
                  {travelPurposeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Field>
                <ErrorMessage
                  name="travelPurpose"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Field
                    type="checkbox"
                    id="travelWithOthers"
                    name="travelWithOthers"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="travelWithOthers" className="ml-2 block text-gray-700 font-medium">
                    Путешествуете с кем-либо?
                  </label>
                </div>
                
                {values.travelWithOthers && (
                  <div className="mt-4 mb-4 p-4 border border-gray-200 rounded-md">
                    <div className="mb-4">
                      <div className="flex items-center mb-2">
                        <Field
                          type="checkbox"
                          id="travelAsGroup"
                          name="travelAsGroup"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label htmlFor="travelAsGroup" className="ml-2 block text-gray-700 font-medium">
                          Путешествуете как часть группы или организации?
                        </label>
                      </div>
                      
                      {values.travelAsGroup ? (
                        <div className="mt-2">
                          <Field
                            type="text"
                            id="groupName"
                            name="groupName"
                            placeholder="Название группы или организации"
                            className="form-input"
                          />
                          <ErrorMessage
                            name="groupName"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                      ) : (
                        <div className="mt-4">
                          <label className="block text-gray-700 font-medium mb-2">
                            Информация о попутчиках
                          </label>
                          <FieldArray name="companions">
                            {({ push, remove }) => (
                              <div>
                                {values.companions && values.companions.length > 0 ? (
                                  values.companions.map((companion, index) => (
                                    <div key={index} className="flex flex-col md:flex-row gap-4 mb-4">
                                      <div className="flex-1">
                                        <Field
                                          type="text"
                                          name={`companions.${index}.name`}
                                          placeholder="Имя и фамилия"
                                          className="form-input"
                                        />
                                        <ErrorMessage
                                          name={`companions.${index}.name`}
                                          component="div"
                                          className="text-red-500 text-sm mt-1"
                                        />
                                      </div>
                                      <div className="flex-1">
                                        <Field
                                          type="text"
                                          name={`companions.${index}.relationship`}
                                          placeholder="Кем приходится"
                                          className="form-input"
                                        />
                                        <ErrorMessage
                                          name={`companions.${index}.relationship`}
                                          component="div"
                                          className="text-red-500 text-sm mt-1"
                                        />
                                      </div>
                                      <button
                                        type="button"
                                        className="bg-red-500 text-white px-3 py-2 rounded"
                                        onClick={() => remove(index)}
                                      >
                                        Удалить
                                      </button>
                                    </div>
                                  ))
                                ) : (
                                  <div className="text-gray-500 mb-2">Нет добавленных попутчиков</div>
                                )}
                                <button
                                  type="button"
                                  className="bg-blue-500 text-white px-4 py-2 rounded"
                                  onClick={() => push({ name: '', relationship: '' })}
                                >
                                  Добавить попутчика
                                </button>
                              </div>
                            )}
                          </FieldArray>
                          {values.companions && values.companions.length === 0 && (
                            <ErrorMessage
                              name="companions"
                              component="div"
                              className="text-red-500 text-sm mt-1"
                            />
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default Step4_TravelPurpose; 