import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Formik, Form, Field, ErrorMessage, FieldArray, FormikProps } from 'formik';
import { step6Schema } from '../utils/validations';
import { Step6Data } from '../utils/types';

export interface Step6Ref {
  submitForm: () => void;
  isValid: boolean;
}

interface Step6Props {
  initialValues: Step6Data;
  onSubmit: (values: Step6Data) => void;
}

const Step6_ContactInfo = forwardRef<Step6Ref, Step6Props>(({ initialValues, onSubmit }, ref) => {
  const formikRef = useRef<FormikProps<Step6Data>>(null);

  useImperativeHandle(ref, () => ({
    submitForm: () => {
      formikRef.current?.submitForm();
    },
    isValid: formikRef.current?.isValid ?? false,
  }));

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Контактная информация</h2>
      <p className="text-gray-600 mb-6">
        Пожалуйста, предоставьте вашу контактную информацию и ссылки на социальные сети.
      </p>

      <Formik
        innerRef={formikRef}
        initialValues={initialValues}
        validationSchema={step6Schema}
        onSubmit={onSubmit}
      >
        {({ values, isValid: _isValid }) => (
          <Form>
            <div className="grid grid-cols-1 gap-4">
              <div className="mb-4">
                <label htmlFor="address" className="block text-gray-700 font-medium mb-2">
                  Домашний адрес (улица, дом)
                </label>
                <Field
                  type="text"
                  id="address"
                  name="address"
                  className="form-input"
                />
                <ErrorMessage
                  name="address"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="mb-4">
                  <label htmlFor="city" className="block text-gray-700 font-medium mb-2">
                    Город
                  </label>
                  <Field
                    type="text"
                    id="city"
                    name="city"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="city"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="country" className="block text-gray-700 font-medium mb-2">
                    Страна
                  </label>
                  <Field
                    type="text"
                    id="country"
                    name="country"
                    className="form-input"
                  />
                  <ErrorMessage
                    name="country"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                </div>
              </div>

              <div className="mb-4">
                <label htmlFor="zipCode" className="block text-gray-700 font-medium mb-2">
                  Почтовый индекс
                </label>
                <Field
                  type="text"
                  id="zipCode"
                  name="zipCode"
                  className="form-input"
                />
                <ErrorMessage
                  name="zipCode"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                  Телефон (+7 ХХХ ХХХ ХХ ХХ)
                </label>
                <Field
                  type="text"
                  id="phone"
                  name="phone"
                  className="form-input"
                  placeholder="+7 XXX XXX XX XX"
                />
                <ErrorMessage
                  name="phone"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                  Email
                </label>
                <Field
                  type="email"
                  id="email"
                  name="email"
                  className="form-input"
                />
                <ErrorMessage
                  name="email"
                  component="div"
                  className="text-red-500 text-sm mt-1"
                />
              </div>

              <div className="mb-4">
                <label className="block text-gray-700 font-medium mb-2">
                  Ссылки на социальные сети (желательно минимум одна)
                </label>
                <FieldArray name="socialMediaLinks">
                  {({ push, remove }) => (
                    <div>
                      {values.socialMediaLinks && values.socialMediaLinks.length > 0 ? (
                        values.socialMediaLinks.map((link, index) => (
                          <div key={index} className="flex items-center gap-2 mb-2">
                            <Field
                              type="text"
                              name={`socialMediaLinks.${index}`}
                              className="form-input flex-1"
                              placeholder="https://..."
                            />
                            <button
                              type="button"
                              className="bg-red-500 text-white px-2 py-1 rounded"
                              onClick={() => remove(index)}
                            >
                              Удалить
                            </button>
                          </div>
                        ))
                      ) : (
                        <div className="text-gray-500 mb-2">Нет добавленных ссылок</div>
                      )}
                      <button
                        type="button"
                        className="bg-blue-500 text-white px-4 py-2 rounded"
                        onClick={() => push('')}
                      >
                        Добавить ссылку
                      </button>
                    </div>
                  )}
                </FieldArray>
                {values.socialMediaLinks && values.socialMediaLinks.length === 0 && (
                  <ErrorMessage
                    name="socialMediaLinks"
                    component="div"
                    className="text-red-500 text-sm mt-1"
                  />
                )}
              </div>
            </div>

            {/* Hidden submit button - will be triggered by StepWrapper */}
            <button type="submit" style={{ display: 'none' }} />
          </Form>
        )}
      </Formik>
    </div>
  );
});

Step6_ContactInfo.displayName = 'Step6_ContactInfo';

export default Step6_ContactInfo;