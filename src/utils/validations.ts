import * as Yup from 'yup';

// Basic field validations
export const validatePhone = (phone: string) =>
  /^\+7\s?\d{3}\s?\d{3}\s?\d{2}\s?\d{2}$/.test(phone);

export const validateEmail = (email: string) =>
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

export const validateIIN = (iin: string) =>
  /^\d{12}$/.test(iin);

export const validateIDNumber = (idNumber: string) =>
  /^\d{9}$/.test(idNumber);

// Yup validation schemas for each step
export const step1Schema = Yup.object({
  visaDestination: Yup.string().required('Страна подачи обязательна'),
});

export const step2Schema = Yup.object({
  surname: Yup.string().required('Фамилия обязательна'),
  name: Yup.string().required('Имя обязательно'),
  dateOfBirth: Yup.date().required('Дата рождения обязательна'),
  citizenship: Yup.string().required('Гражданство обязательно'),
  passportNumber: Yup.string().required('Номер паспорта обязателен'),
  passportIssueDate: Yup.date().required('Дата выдачи паспорта обязательна'),
  passportExpiryDate: Yup.date().required('Дата окончания паспорта обязательна'),
  iin: Yup.string()
    .matches(/^\d{12}$/, 'ИИН должен содержать 12 цифр')
    .required('ИИН обязателен'),
  idNumber: Yup.string()
    .matches(/^\d{9}$/, 'Номер удостоверения должен содержать 9 цифр')
    .required('Номер удостоверения обязателен'),
});

export const step3Schema = Yup.object({
  fullNameCyrillic: Yup.string().required('ФИО на кириллице обязательно'),
  hasOtherNames: Yup.boolean().required(),
  otherNames: Yup.string().when('hasOtherNames', {
    is: true,
    then: (schema) => schema.required('Необходимо указать другие имена'),
    otherwise: (schema) => schema,
  }),
  gender: Yup.string().oneOf(['male', 'female']).required('Пол обязателен'),
  maritalStatus: Yup.string().required('Семейное положение обязательно'),
  cityOfBirth: Yup.string().required('Город рождения обязателен'),
  countryOfBirth: Yup.string().required('Страна рождения обязательна'),
  hasOtherCitizenship: Yup.boolean().required(),
  otherCitizenship: Yup.string().when('hasOtherCitizenship', {
    is: true,
    then: (schema) => schema.required('Необходимо указать другое гражданство'),
    otherwise: (schema) => schema,
  }),
  isPermanentResidentOtherCountry: Yup.boolean().required(),
  permanentResidenceCountry: Yup.string().when('isPermanentResidentOtherCountry', {
    is: true,
    then: (schema) => schema.required('Необходимо указать страну постоянного проживания'),
    otherwise: (schema) => schema,
  }),
  nationality: Yup.string().required('Национальность обязательна'),
  hasSSN: Yup.boolean().required(),
  ssn: Yup.string().when('hasSSN', {
    is: true,
    then: (schema) => schema.required('Необходимо указать SSN'),
    otherwise: (schema) => schema,
  }),
  hasTaxpayerId: Yup.boolean().required(),
  taxpayerId: Yup.string().when('hasTaxpayerId', {
    is: true,
    then: (schema) => schema.required('Необходимо указать Taxpayer ID'),
    otherwise: (schema) => schema,
  }),
});

export const step4Schema = Yup.object({
  travelPurpose: Yup.string()
    .oneOf(['tourism', 'business', 'medical'])
    .required('Цель поездки обязательна'),
  travelWithOthers: Yup.boolean().required(),
  travelAsGroup: Yup.boolean().when('travelWithOthers', {
    is: true,
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  groupName: Yup.string().when(['travelWithOthers', 'travelAsGroup'], {
    is: (travelWithOthers: boolean, travelAsGroup: boolean) => travelWithOthers && travelAsGroup,
    then: (schema) => schema.required('Необходимо указать название группы'),
    otherwise: (schema) => schema,
  }),
  companions: Yup.array().when(['travelWithOthers', 'travelAsGroup'], {
    is: (travelWithOthers: boolean, travelAsGroup: boolean) => travelWithOthers && !travelAsGroup,
    then: (schema) => schema.min(1, 'Добавьте хотя бы одного попутчика'),
    otherwise: (schema) => schema,
  }),
});

export const step5Schema = Yup.object({
  hasBeenToUSA: Yup.boolean().required(),
  hasUSVisa: Yup.boolean().required(),
  lastVisaDate: Yup.date().when('hasUSVisa', {
    is: true,
    then: (schema) => schema.required('Дата последней визы обязательна'),
    otherwise: (schema) => schema,
  }),
  visaNumber: Yup.string().when('hasUSVisa', {
    is: true,
    then: (schema) => schema.required('Номер визы обязателен'),
    otherwise: (schema) => schema,
  }),
  isSameVisaType: Yup.boolean().when('hasUSVisa', {
    is: true,
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  isSameCountry: Yup.boolean().when('hasUSVisa', {
    is: true,
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  hasVisaRejections: Yup.boolean().required(),
  rejectionVisaType: Yup.string().when('hasVisaRejections', {
    is: true,
    then: (schema) => schema.required('Укажите тип визы, в которой отказали'),
    otherwise: (schema) => schema,
  }),
  rejectionDate: Yup.date().when('hasVisaRejections', {
    is: true,
    then: (schema) => schema.required('Дата отказа обязательна'),
    otherwise: (schema) => schema,
  }),
});

export const step6Schema = Yup.object({
  address: Yup.string().required('Адрес обязателен'),
  city: Yup.string().required('Город обязателен'),
  country: Yup.string().required('Страна обязательна'),
  zipCode: Yup.string().required('Индекс обязателен'),
  phone: Yup.string()
    .matches(/^\+7\s?\d{3}\s?\d{3}\s?\d{2}\s?\d{2}$/, 'Неверный формат телефона')
    .required('Телефон обязателен'),
  email: Yup.string()
    .email('Неверный формат email')
    .required('Email обязателен'),
  socialMediaLinks: Yup.array()
    .of(Yup.string().url('Неверный формат ссылки'))
    .min(1, 'Добавьте хотя бы одну ссылку на соцсеть'),
});

export const step7Schema = Yup.object({
  fatherSurname: Yup.string().required('Фамилия отца обязательна'),
  fatherName: Yup.string().required('Имя отца обязательно'),
  fatherDateOfBirth: Yup.date().when('isFatherDateOfBirthUnknown', {
    is: false,
    then: (schema) => schema.required('Дата рождения отца обязательна'),
    otherwise: (schema) => schema,
  }),
  isFatherDateOfBirthUnknown: Yup.boolean().required(),
  isFatherInUSA: Yup.boolean().required(),
  fatherUSAReason: Yup.string().when('isFatherInUSA', {
    is: true,
    then: (schema) => schema.required('Укажите причину пребывания отца в США'),
    otherwise: (schema) => schema,
  }),
  motherSurname: Yup.string().required('Фамилия матери обязательна'),
  motherName: Yup.string().required('Имя матери обязательно'),
  motherDateOfBirth: Yup.date().when('isMotherDateOfBirthUnknown', {
    is: false,
    then: (schema) => schema.required('Дата рождения матери обязательна'),
    otherwise: (schema) => schema,
  }),
  isMotherDateOfBirthUnknown: Yup.boolean().required(),
  isMotherInUSA: Yup.boolean().required(),
  motherUSAReason: Yup.string().when('isMotherInUSA', {
    is: true,
    then: (schema) => schema.required('Укажите причину пребывания матери в США'),
    otherwise: (schema) => schema,
  }),
  hasRelativesInUSA: Yup.boolean().required(),
  relatives: Yup.array().when('hasRelativesInUSA', {
    is: true,
    then: (schema) => schema.min(1, 'Добавьте хотя бы одного родственника'),
    otherwise: (schema) => schema,
  }),
});

export const step8Schema = Yup.object({
  occupation: Yup.string()
    .oneOf(['employed', 'student', 'unemployed', 'business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'])
    .required('Род занятий обязателен'),

  // Employment fields
  companyName: Yup.string().when('occupation', {
    is: 'employed',
    then: (schema) => schema.required('Название компании обязательно'),
    otherwise: (schema) => schema,
  }),
  position: Yup.string().when('occupation', {
    is: 'employed',
    then: (schema) => schema.required('Должность обязательна'),
    otherwise: (schema) => schema,
  }),
  workAddress: Yup.string().when('occupation', {
    is: 'employed',
    then: (schema) => schema.required('Адрес работы обязателен'),
    otherwise: (schema) => schema,
  }),
  workPhone: Yup.string().when('occupation', {
    is: 'employed',
    then: (schema) => schema.required('Телефон работы обязателен'),
    otherwise: (schema) => schema,
  }),
  workExperience: Yup.string().when('occupation', {
    is: 'employed',
    then: (schema) => schema.required('Стаж работы обязателен'),
    otherwise: (schema) => schema,
  }),
  income: Yup.string().when('occupation', {
    is: 'employed',
    then: (schema) => schema.required('Ежемесячный доход обязателен'),
    otherwise: (schema) => schema,
  }),

  // Student fields
  universityName: Yup.string().when('occupation', {
    is: 'student',
    then: (schema) => schema.required('Название университета обязательно'),
    otherwise: (schema) => schema,
  }),
  universityAddress: Yup.string().when('occupation', {
    is: 'student',
    then: (schema) => schema.required('Адрес университета обязателен'),
    otherwise: (schema) => schema,
  }),
  faculty: Yup.string().when('occupation', {
    is: 'student',
    then: (schema) => schema.required('Факультет обязателен'),
    otherwise: (schema) => schema,
  }),
  startDate: Yup.date().when('occupation', {
    is: 'student',
    then: (schema) => schema.required('Дата начала обучения обязательна'),
    otherwise: (schema) => schema,
  }),
  endDate: Yup.date().when('occupation', {
    is: 'student',
    then: (schema) => schema.required('Дата окончания обучения обязательна'),
    otherwise: (schema) => schema,
  }),

  // Business fields
  businessType: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required('Тип деятельности обязателен'),
    otherwise: (schema) => schema,
  }),
  businessName: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur'].includes(value),
    then: (schema) => schema.required('Название бизнеса/ИП обязательно'),
    otherwise: (schema) => schema,
  }),
  businessRegistrationType: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur'].includes(value),
    then: (schema) => schema.required('Форма регистрации обязательна'),
    otherwise: (schema) => schema,
  }),
  businessRegistrationNumber: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur'].includes(value),
    then: (schema) => schema.required('Регистрационный номер обязателен'),
    otherwise: (schema) => schema,
  }),
  businessRegistrationDate: Yup.date().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur'].includes(value),
    then: (schema) => schema.required('Дата регистрации обязательна'),
    otherwise: (schema) => schema,
  }),
  businessActivity: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required('Основной вид деятельности обязателен'),
    otherwise: (schema) => schema,
  }),
  monthlyBusinessIncome: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required('Среднемесячный доход обязателен'),
    otherwise: (schema) => schema,
  }),
  hasEmployees: Yup.boolean().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur'].includes(value),
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  employeeCount: Yup.number().when(['occupation', 'hasEmployees'], {
    is: (occupation: string, hasEmployees: boolean) =>
      ['business_owner', 'individual_entrepreneur'].includes(occupation) && hasEmployees,
    then: (schema) => schema.min(1, 'Количество сотрудников должно быть больше 0').required('Количество сотрудников обязательно'),
    otherwise: (schema) => schema,
  }),
  businessStatus: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur'].includes(value),
    then: (schema) => schema.required('Статус в бизнесе обязателен'),
    otherwise: (schema) => schema,
  }),
  businessAddress: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur'].includes(value),
    then: (schema) => schema.required('Адрес регистрации бизнеса обязателен'),
    otherwise: (schema) => schema,
  }),
  businessWebsite: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.url('Неверный формат URL'),
    otherwise: (schema) => schema,
  }),
  hasInternationalClients: Yup.boolean().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  hasPermanentContracts: Yup.boolean().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  paysTaxes: Yup.boolean().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  businessExperienceYears: Yup.number().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.min(0, 'Опыт не может быть отрицательным').required('Количество лет деятельности обязательно'),
    otherwise: (schema) => schema,
  }),
  hasBankStatements: Yup.boolean().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  yearlyIncome: Yup.string().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required('Примерный годовой доход обязателен'),
    otherwise: (schema) => schema,
  }),
  hasOffice: Yup.boolean().when('occupation', {
    is: (value: string) => ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(value),
    then: (schema) => schema.required(),
    otherwise: (schema) => schema,
  }),
  officeAddress: Yup.string().when(['occupation', 'hasOffice'], {
    is: (occupation: string, hasOffice: boolean) =>
      ['business_owner', 'individual_entrepreneur', 'self_employed', 'freelancer'].includes(occupation) && hasOffice,
    then: (schema) => schema.required('Адрес офиса обязателен'),
    otherwise: (schema) => schema,
  }),
});

export const step9Schema = Yup.object({
  visitedCountries: Yup.array()
    .min(1, 'Укажите хотя бы одну страну')
    .required('Список стран обязателен'),
});