// Form field types
export interface VisaFormData {
  // Step 1
  country: string;
  
  // Step 2
  surname: string;
  name: string;
  dateOfBirth: string;
  citizenship: string;
  passportNumber: string;
  passportIssueDate: string;
  passportExpiryDate: string;
  iin: string;
  idNumber: string;
  
  // Step 3
  fullNameCyrillic: string;
  hasOtherNames: boolean;
  otherNames?: string;
  gender: string;
  maritalStatus: string;
  cityOfBirth: string;
  countryOfBirth: string;
  hasOtherCitizenship: boolean;
  otherCitizenship?: string;
  isPermanentResidentOtherCountry: boolean;
  permanentResidenceCountry?: string;
  nationality: string;
  hasSSN: boolean;
  ssn?: string;
  hasTaxpayerId: boolean;
  taxpayerId?: string;
  
  // Step 4
  travelPurpose?: string;
  travelWithOthers?: boolean;
  travelAsGroup?: boolean;
  groupName?: string;
  companions?: Array<{ name: string; relationship: string }>;
  
  // Step 5
  hasBeenToUSA?: boolean;
  hasUSVisa?: boolean;
  lastVisaDate?: string;
  visaNumber?: string;
  isSameVisaType?: boolean;
  isSameCountry?: boolean;
  hasVisaRejections?: boolean;
  rejectionVisaType?: string;
  rejectionDate?: string;
  
  // Step 6
  address?: string;
  city?: string;
  zipCode?: string;
  phone?: string;
  email?: string;
  socialMediaLinks?: string[];
  
  // Step 7
  fatherSurname?: string;
  fatherName?: string;
  fatherDateOfBirth?: string;
  isFatherDateOfBirthUnknown?: boolean;
  isFatherInUSA?: boolean;
  fatherUSAReason?: string;
  motherSurname?: string;
  motherName?: string;
  motherDateOfBirth?: string;
  isMotherDateOfBirthUnknown?: boolean;
  isMotherInUSA?: boolean;
  motherUSAReason?: string;
  hasRelativesInUSA?: boolean;
  relatives?: Array<{ name: string; relationship: string }>;
  
  // Step 8
  occupation?: string;
  // Employment fields
  companyName?: string;
  position?: string;
  workAddress?: string;
  workPhone?: string;
  workExperience?: string;
  income?: string;
  // Student fields
  universityName?: string;
  universityAddress?: string;
  faculty?: string;
  startDate?: string;
  endDate?: string;
  // Business/IP/Self-employed fields
  businessType?: string;
  businessName?: string;
  businessRegistrationType?: string;
  businessRegistrationNumber?: string;
  businessRegistrationDate?: string;
  businessActivity?: string;
  monthlyBusinessIncome?: string;
  hasEmployees?: boolean;
  employeeCount?: number;
  businessStatus?: string;
  businessAddress?: string;
  businessWebsite?: string;
  hasInternationalClients?: boolean;
  hasPermanentContracts?: boolean;
  paysTaxes?: boolean;
  businessExperienceYears?: number;
  hasBankStatements?: boolean;
  yearlyIncome?: string;
  hasOffice?: boolean;
  officeAddress?: string;
  
  // Step 9
  visitedCountries?: string[];
  
  // Any additional fields
  [key: string]: string | boolean | number | Array<unknown> | object | undefined;
}

// Step-specific interfaces
export interface Step1Data {
  country: string;
}

export interface Step2Data {
  surname: string;
  name: string;
  dateOfBirth: string;
  citizenship: string;
  passportNumber: string;
  passportIssueDate: string;
  passportExpiryDate: string;
  iin: string;
  idNumber: string;
}

export interface Step3Data {
  fullNameCyrillic: string;
  hasOtherNames: boolean;
  otherNames?: string;
  gender: string;
  maritalStatus: string;
  cityOfBirth: string;
  countryOfBirth: string;
  hasOtherCitizenship: boolean;
  otherCitizenship?: string;
  isPermanentResidentOtherCountry: boolean;
  permanentResidenceCountry?: string;
  nationality: string;
  hasSSN: boolean;
  ssn?: string;
  hasTaxpayerId: boolean;
  taxpayerId?: string;
}

export interface Step4Data {
  travelPurpose: string;
  travelWithOthers: boolean;
  travelAsGroup?: boolean;
  groupName?: string;
  companions?: Array<{ name: string; relationship: string }>;
}

export interface Step5Data {
  hasBeenToUSA: boolean;
  hasUSVisa: boolean;
  lastVisaDate?: string;
  visaNumber?: string;
  isSameVisaType?: boolean;
  isSameCountry?: boolean;
  hasVisaRejections: boolean;
  rejectionVisaType?: string;
  rejectionDate?: string;
}

export interface Step6Data {
  address: string;
  city: string;
  country: string;
  zipCode: string;
  phone: string;
  email: string;
  socialMediaLinks: string[];
}

export interface Step7Data {
  fatherSurname: string;
  fatherName: string;
  fatherDateOfBirth?: string;
  isFatherDateOfBirthUnknown: boolean;
  isFatherInUSA: boolean;
  fatherUSAReason?: string;
  motherSurname: string;
  motherName: string;
  motherDateOfBirth?: string;
  isMotherDateOfBirthUnknown: boolean;
  isMotherInUSA: boolean;
  motherUSAReason?: string;
  hasRelativesInUSA: boolean;
  relatives?: Array<{ name: string; relationship: string }>;
}

export interface Step8Data {
  occupation: string;
  // Employment fields
  companyName?: string;
  position?: string;
  workAddress?: string;
  workPhone?: string;
  workExperience?: string;
  income?: string;
  // Student fields
  universityName?: string;
  universityAddress?: string;
  faculty?: string;
  startDate?: string;
  endDate?: string;
  // Business/IP/Self-employed fields
  businessType?: string;
  businessName?: string;
  businessRegistrationType?: string;
  businessRegistrationNumber?: string;
  businessRegistrationDate?: string;
  businessActivity?: string;
  monthlyBusinessIncome?: string;
  hasEmployees?: boolean;
  employeeCount?: number;
  businessStatus?: string;
  businessAddress?: string;
  businessWebsite?: string;
  hasInternationalClients?: boolean;
  hasPermanentContracts?: boolean;
  paysTaxes?: boolean;
  businessExperienceYears?: number;
  hasBankStatements?: boolean;
  yearlyIncome?: string;
  hasOffice?: boolean;
  officeAddress?: string;
}

export interface Step9Data {
  visitedCountries: string[];
} 